#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def parse_create_table_statement(sql_statement):
    """
    解析CREATE TABLE语句并生成易读的格式
    
    Args:
        sql_statement: CREATE TABLE语句
        
    Returns:
        格式化后的表结构描述
    """
    try:
        # 移除多余的空白字符和换行
        sql = re.sub(r'\s+', ' ', sql_statement.strip())
        
        # 提取表名
        table_match = re.search(r'CREATE TABLE \[([^\]]+)\]', sql, re.IGNORECASE)
        if not table_match:
            return "无法解析表名"
        
        table_name = table_match.group(1)
        
        # 根据表名推测表的中文描述
        table_descriptions = {
            'user': '用户表',
            'users': '用户表', 
            'sp_user': '用户表',
            'employee': '员工表',
            'staff': '员工表',
            'order': '订单表',
            'orders': '订单表',
            'product': '产品表',
            'products': '产品表',
            'company': '公司表',
            'department': '部门表',
            'award': '奖项表',
            'awards': '奖项表',
            'companyawards': '公司奖项表',
            'balance': '余额表',
            'balancetable': '余额表',
            'financial': '财务表',
            'finance': '财务表'
        }
        
        # 查找匹配的表描述
        table_desc = '数据表'
        table_name_lower = table_name.lower()
        for key, desc in table_descriptions.items():
            if key in table_name_lower:
                table_desc = desc
                break
        
        # 提取字段定义部分
        fields_match = re.search(r'\((.+)\)', sql, re.DOTALL)
        if not fields_match:
            return f"表名：[{table_name}] {table_desc}\n无法解析字段信息"
        
        fields_content = fields_match.group(1)
        
        # 分割字段定义
        field_lines = []
        current_field = ""
        paren_count = 0
        
        for char in fields_content:
            if char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
            elif char == ',' and paren_count == 0:
                if current_field.strip():
                    field_lines.append(current_field.strip())
                current_field = ""
                continue
            current_field += char
        
        if current_field.strip():
            field_lines.append(current_field.strip())
        
        # 解析每个字段
        formatted_fields = []
        primary_keys = []
        
        for field_line in field_lines:
            field_line = field_line.strip()
            
            # 跳过约束定义
            if (field_line.upper().startswith('CONSTRAINT') or 
                field_line.upper().startswith('PRIMARY KEY') or
                field_line.upper().startswith('FOREIGN KEY') or
                field_line.upper().startswith('INDEX') or
                field_line.upper().startswith('KEY')):
                
                # 提取主键信息
                if 'PRIMARY KEY' in field_line.upper():
                    pk_match = re.search(r'PRIMARY KEY\s*\(\s*\[([^\]]+)\]', field_line, re.IGNORECASE)
                    if pk_match:
                        primary_keys.append(pk_match.group(1))
                continue
            
            # 解析字段定义
            field_info = parse_field_definition(field_line)
            if field_info:
                formatted_fields.append(field_info)
        
        # 标记主键字段
        for field in formatted_fields:
            if field['name'] in primary_keys:
                field['is_primary'] = True
        
        # 生成格式化输出
        result = f"表名：[{table_name}]  {table_desc}\n"
        
        if formatted_fields:
            result += "      字段："
            for i, field in enumerate(formatted_fields):
                if i > 0:
                    result += "                 "
                
                field_desc = field['name']
                field_desc += f"  {field['type']}"
                
                if field.get('is_primary'):
                    field_desc += "  主键"
                elif field.get('not_null'):
                    field_desc += "  非空"
                
                if field.get('comment'):
                    field_desc += f"  {field['comment']}"
                else:
                    # 根据字段名推测中文描述
                    field_desc += f"  {get_field_description(field['name'])}"
                
                result += field_desc + "\n"
        else:
            result += "      字段：无法解析字段信息\n"
        
        return result.rstrip()
        
    except Exception as e:
        return f"解析错误: {str(e)}"

def parse_field_definition(field_line):
    """
    解析单个字段定义
    """
    try:
        # 提取字段名（在方括号中）
        name_match = re.search(r'\[([^\]]+)\]', field_line)
        if not name_match:
            return None
        
        field_name = name_match.group(1)
        
        # 移除字段名部分，获取剩余定义
        remaining = field_line[name_match.end():].strip()
        
        # 提取数据类型
        type_match = re.match(r'([A-Za-z]+(?:\([^)]+\))?)', remaining)
        if not type_match:
            return None
        
        field_type = type_match.group(1)
        
        # 检查约束
        is_primary = 'PRIMARY KEY' in field_line.upper()
        not_null = 'NOT NULL' in field_line.upper()
        auto_increment = 'AUTO_INCREMENT' in field_line.upper()
        
        return {
            'name': field_name,
            'type': field_type.lower(),
            'is_primary': is_primary,
            'not_null': not_null,
            'auto_increment': auto_increment,
            'comment': None
        }
        
    except Exception as e:
        return None

def get_field_description(field_name):
    """
    根据字段名推测中文描述
    """
    field_descriptions = {
        'id': 'ID',
        'user_id': '用户ID',
        'username': '用户名',
        'password': '密码',
        'email': '邮箱',
        'phone': '电话',
        'mobile': '手机号',
        'name': '名称',
        'title': '标题',
        'content': '内容',
        'description': '描述',
        'status': '状态',
        'type': '类型',
        'category': '分类',
        'price': '价格',
        'amount': '金额',
        'quantity': '数量',
        'date': '日期',
        'time': '时间',
        'created_at': '创建时间',
        'updated_at': '更新时间',
        'create_time': '创建时间',
        'update_time': '更新时间'
    }
    
    field_name_lower = field_name.lower()
    
    # 精确匹配
    if field_name_lower in field_descriptions:
        return field_descriptions[field_name_lower]
    
    # 模糊匹配
    for key, desc in field_descriptions.items():
        if key in field_name_lower or field_name_lower in key:
            return desc
    
    # 默认描述
    return field_name

# 测试
if __name__ == "__main__":
    test_sql = """
    CREATE TABLE [sp_user]
    (
        [id] BIGINT NOT NULL AUTO_INCREMENT,
        [username] VARCHAR(200) NOT NULL,
        [email] VARCHAR(255),
        [created_at] DATETIME,
        PRIMARY KEY ([id])
    );
    """
    
    result = parse_create_table_statement(test_sql)
    print(result)
