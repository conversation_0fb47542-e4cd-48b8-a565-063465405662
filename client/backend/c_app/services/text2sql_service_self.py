import asyncio
import json
import re
from re import sub
import time
import numpy as np
from typing import Dict, List, Optional, Any, Tuple, Awaitable, Callable, Union, TypedDict
from dataclasses import dataclass, asdict

# Milvus
try:
    from pymilvus import (
        connections,
        utility,
        FieldSchema,
        CollectionSchema,
        DataType,
        Collection,
    )
    MILVUS_AVAILABLE = True
except ImportError:
    MILVUS_AVAILABLE = False
    print("Warning: pymilvus not installed. Milvus functionality will be disabled.")

# 中文向量模型
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    print("Warning: sentence-transformers not installed. Will use random embeddings.")

from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.base import TaskResult
from autogen_agentchat.conditions import TextMentionTermination
from autogen_agentchat.messages import ModelClientStreamingChunkEvent, TextMessage, UserInputRequestedEvent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_core import message_handler, RoutedAgent, SingleThreadedAgentRuntime, DefaultTopicId, TopicId, \
    type_subscription, AgentId, MessageContext, CancellationToken, ClosureContext
from autogen_core.memory import ListMemory, MemoryContent, MemoryMimeType
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core import ClosureAgent, TypeSubscription
from pydantic import BaseModel

from ..core.config import settings
from ..core.llms import model_client
from ..schemas.text2sql import (
    Text2SQLResponse, ResponseMessage, QueryMessage, SqlMessage,
    SqlExplanationMessage, SqlResultMessage, VisualizationMessage, AnalysisMessage
)
from ..db.dbaccess import DBAccess
from re import sub

# 定义主题类型
sql_generator_topic_type = "sql_generator"
sql_explainer_topic_type = "sql_explainer"
sql_executor_topic_type = "sql_executor"
visualization_recommender_topic_type = "visualization_recommender"
stream_output_topic_type = "stream_output"
query_analyzer_topic_type = "query_analyzer"

# 定义数据库类型（可配置）
DB_TYPE = "MySQL"  # 可选值: "MySQL", "PostgreSQL", "SQLite", "Oracle", "SQL Server"

# 初始化数据库连接
dbAccess = DBAccess()
try:
    dbAccess.connect_to_mysql(host="127.0.0.1", dbname="dyl_srm", user="root", password="root", port=3306)
    print("Successfully connected to MySQL database")
except Exception as e:
    print(f"Failed to connect to MySQL database: {str(e)}")
    print("Application will continue without database connection")
    # 可以在这里设置一个标志，表示数据库连接失败
    dbAccess = None

# dbAccess.connect_to_mysql(host="*************", dbname="dongying", user="root", password="twf65132090TWF", port=3306)
#dbAccess.connect_to_sqlite("https://vanna.ai/Chinook.sqlite")
# df_ddl = dbAccess.run_sql("SELECT type,sql FROM sqlite_master WHERE sql is not null")
# db_schema_definition = ''

# Milvus配置
MILVUS_HOST = "127.0.0.1"
MILVUS_PORT = "19530"  # 使用用户指定的端口
MILVUS_COLLECTION_NAME = "db_schema_statements"
MILVUS_DIMENSION = 512  # 向量维度，使用BGE-small-zh-v1.5的输出维度

# 中文向量模型配置
CHINESE_EMBEDDING_MODEL = "BAAI/bge-small-zh-v1.5"

# 初始化向量模型
embedding_model = None
if SENTENCE_TRANSFORMERS_AVAILABLE:
    try:
        print(f"Loading Chinese embedding model: {CHINESE_EMBEDDING_MODEL}")
        embedding_model = SentenceTransformer(CHINESE_EMBEDDING_MODEL)
        print("Chinese embedding model loaded successfully")
    except Exception as e:
        print(f"Failed to load Chinese embedding model: {str(e)}")
        embedding_model = None

# 连接到Milvus
def connect_to_milvus():
    """连接到Milvus数据库"""
    if not MILVUS_AVAILABLE:
        print("Milvus functionality is disabled because pymilvus is not installed.")
        return False

    try:
        connections.connect("default", host=MILVUS_HOST, port=MILVUS_PORT)
        print(f"Successfully connected to Milvus server at {MILVUS_HOST}:{MILVUS_PORT}")
        return True
    except Exception as e:
        print(f"Failed to connect to Milvus: {str(e)}")
        return False

# 创建Milvus集合
def create_milvus_collection():
    """创建Milvus集合用于存储建表语句"""
    if not MILVUS_AVAILABLE:
        return None

    try:
        # 检查集合是否存在
        if utility.has_collection(MILVUS_COLLECTION_NAME):
            print(f"Collection {MILVUS_COLLECTION_NAME} already exists.")
            return Collection(MILVUS_COLLECTION_NAME)

        # 定义集合结构
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
            FieldSchema(name="table_name", dtype=DataType.VARCHAR, max_length=100),
            FieldSchema(name="statement", dtype=DataType.VARCHAR, max_length=65535),  # 存储建表语句
            FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=MILVUS_DIMENSION)  # 存储向量
        ]
        schema = CollectionSchema(fields=fields, description="Database schema statements collection")

        # 创建集合
        collection = Collection(name=MILVUS_COLLECTION_NAME, schema=schema)

        # 创建索引，使用余弦相似度
        index_params = {
            "index_type": "IVF_FLAT",
            "metric_type": "IP",  # Inner Product，适合归一化向量
            "params": {"nlist": 128}
        }
        collection.create_index(field_name="embedding", index_params=index_params)
        print(f"Created index for collection {MILVUS_COLLECTION_NAME}")
        print(f"Successfully created collection {MILVUS_COLLECTION_NAME}")

        return collection
    except Exception as e:
        print(f"Failed to create Milvus collection: {str(e)}")
        return None

# 生成文本向量
def generate_embedding(text):
    """
    使用中文向量模型 BAAI/bge-small-zh-v1.5 生成文本的向量表示

    Args:
        text: 要编码的文本

    Returns:
        文本的向量表示（512维）
    """
    global embedding_model

    if embedding_model is not None:
        try:
            # 使用中文向量模型生成向量
            embeddings = embedding_model.encode([text], normalize_embeddings=True)
            # 返回第一个文本的向量，并转换为float32类型
            return embeddings[0].astype(np.float32).tolist()
        except Exception as e:
            print(f"Error generating embedding with Chinese model: {str(e)}")
            # 如果中文模型失败，回退到随机向量
            pass

    # 如果中文模型不可用或失败，使用随机向量作为备选
    print("Using random embedding as fallback")
    return np.random.random(MILVUS_DIMENSION).astype(np.float32).tolist()

# 将建表语句保存到Milvus
def save_to_milvus(table_name, statement):
    """将建表语句保存到Milvus"""
    if not MILVUS_AVAILABLE:
        return False

    try:
        # 连接到Milvus
        if not connect_to_milvus():
            return False

        # 创建或获取集合
        collection = create_milvus_collection()
        if collection is None:
            return False

        # 生成向量
        embedding = generate_embedding(statement)

        # 准备数据
        data = [
            [table_name],  # table_name字段
            [statement],  # statement字段
            [embedding]    # embedding字段
        ]

        # 插入数据
        insert_result = collection.insert(data)
        collection.flush()

        # 检查插入结果
        if hasattr(insert_result, 'primary_keys') and insert_result.primary_keys:
            print(f"Successfully saved statement for table {table_name} to Milvus (ID: {insert_result.primary_keys[0]})")
        else:
            print(f"Successfully saved statement for table {table_name} to Milvus")

        # 检查集合中的数据数量
        num_entities = collection.num_entities
        print(f"Collection now has {num_entities} entities")

        return True
    except Exception as e:
        print(f"Failed to save to Milvus: {str(e)}")
        return False

# 从 Milvus 中搜索相关的建表语句
def search_milvus(query_text, top_k=3):
    """从 Milvus 中搜索与查询相关的建表语句"""
    if not MILVUS_AVAILABLE:
        return []

    try:
        # 连接到Milvus
        if not connect_to_milvus():
            return []

        # 获取或创建集合
        if not utility.has_collection(MILVUS_COLLECTION_NAME):
            print(f"Collection {MILVUS_COLLECTION_NAME} does not exist. Creating it...")
            collection = create_milvus_collection()
            if collection is None:
                return []
        else:
            collection = Collection(MILVUS_COLLECTION_NAME)

        collection.load()

        # 检查集合中的数据数量
        num_entities = collection.num_entities
        print(f"Collection {MILVUS_COLLECTION_NAME} has {num_entities} entities")

        if num_entities == 0:
            print("Collection is empty. No data to search.")
            return []

        # 生成查询向量
        query_embedding = generate_embedding(query_text)

        # 搜索，使用余弦相似度（更适合归一化向量）
        search_params = {"metric_type": "IP", "params": {"nprobe": 16}}  # IP = Inner Product，适合归一化向量

        print(f"Searching in Milvus with query: '{query_text}'")
        print(f"Query embedding shape: {len(query_embedding)}")

        results = collection.search(
            data=[query_embedding],
            anns_field="embedding",
            param=search_params,
            limit=top_k,
            output_fields=["table_name", "statement"]
        )

        print(f"Search results count: {len(results[0]) if results and len(results) > 0 else 0}")

        # 提取结果
        statements = []
        for hits in results:
            for hit in hits:
                table_name = hit.entity.get('table_name')
                statement = hit.entity.get('statement')
                statements.append({
                    'table_name': table_name,
                    'statement': statement,
                    'score': hit.score
                })

        return statements
    except Exception as e:
        print(f"Failed to search Milvus: {str(e)}")
        return []

# 清理和重建 Milvus 集合
def reset_milvus_collection():
    """清理并重建 Milvus 集合"""
    if not MILVUS_AVAILABLE:
        return False

    try:
        # 连接到Milvus
        if not connect_to_milvus():
            return False

        # 删除旧集合（如果存在）
        if utility.has_collection(MILVUS_COLLECTION_NAME):
            utility.drop_collection(MILVUS_COLLECTION_NAME)
            print(f"Dropped existing collection {MILVUS_COLLECTION_NAME}")

        # 创建新集合
        collection = create_milvus_collection()
        if collection is not None:
            print(f"Successfully reset collection {MILVUS_COLLECTION_NAME}")
            return True
        else:
            return False

    except Exception as e:
        print(f"Failed to reset Milvus collection: {str(e)}")
        return False

# 清除 Milvus 集合中的数据（保留集合结构）
def clear_milvus_data():
    """清除 Milvus 集合中的数据，但保留集合结构"""
    if not MILVUS_AVAILABLE:
        return False

    try:
        # 连接到Milvus
        if not connect_to_milvus():
            return False

        # 检查集合是否存在
        if not utility.has_collection(MILVUS_COLLECTION_NAME):
            print(f"Collection {MILVUS_COLLECTION_NAME} does not exist.")
            return True

        collection = Collection(MILVUS_COLLECTION_NAME)

        # 删除所有数据
        collection.delete(expr="id >= 0")  # 删除所有记录
        collection.flush()

        print(f"Successfully cleared all data from collection {MILVUS_COLLECTION_NAME}")
        return True

    except Exception as e:
        print(f"Failed to clear Milvus data: {str(e)}")
        return False

# 删除整个 Milvus 集合
def drop_milvus_collection():
    """完全删除 Milvus 集合"""
    if not MILVUS_AVAILABLE:
        return False

    try:
        # 连接到Milvus
        if not connect_to_milvus():
            return False

        # 删除集合（如果存在）
        if utility.has_collection(MILVUS_COLLECTION_NAME):
            utility.drop_collection(MILVUS_COLLECTION_NAME)
            print(f"Successfully dropped collection {MILVUS_COLLECTION_NAME}")
            return True
        else:
            print(f"Collection {MILVUS_COLLECTION_NAME} does not exist.")
            return True

    except Exception as e:
        print(f"Failed to drop Milvus collection: {str(e)}")
        return False

# 解析和格式化建表语句
def parse_create_table_statement(sql_statement):
    """
    解析CREATE TABLE语句并生成易读的格式

    Args:
        sql_statement: CREATE TABLE语句

    Returns:
        格式化后的表结构描述
    """
    try:
        # 移除多余的空白字符和换行
        sql = re.sub(r'\s+', ' ', sql_statement.strip())

        # 提取表名
        table_match = re.search(r'CREATE TABLE \[([^\]]+)\]', sql, re.IGNORECASE)
        if not table_match:
            return "无法解析表名"

        table_name = table_match.group(1)

        # 根据表名推测表的中文描述
        table_descriptions = {
            'user': '用户表',
            'users': '用户表',
            'sp_user': '用户表',
            'employee': '员工表',
            'staff': '员工表',
            'order': '订单表',
            'orders': '订单表',
            'product': '产品表',
            'products': '产品表',
            'company': '公司表',
            'department': '部门表',
            'award': '奖项表',
            'awards': '奖项表',
            'companyawards': '公司奖项表',
            'balance': '余额表',
            'balancetable': '余额表',
            'financial': '财务表',
            'finance': '财务表'
        }

        # 查找匹配的表描述
        table_desc = '数据表'
        table_name_lower = table_name.lower()
        for key, desc in table_descriptions.items():
            if key in table_name_lower:
                table_desc = desc
                break

        # 提取字段定义部分
        # 找到第一个 ( 和最后一个 ) 之间的内容
        fields_match = re.search(r'\((.+)\)', sql, re.DOTALL)
        if not fields_match:
            return f"表名：[{table_name}] {table_desc}\n无法解析字段信息"

        fields_content = fields_match.group(1)

        # 分割字段定义
        # 处理复杂的字段定义，包括约束
        field_lines = []
        current_field = ""
        paren_count = 0

        for char in fields_content:
            if char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1
            elif char == ',' and paren_count == 0:
                if current_field.strip():
                    field_lines.append(current_field.strip())
                current_field = ""
                continue
            current_field += char

        if current_field.strip():
            field_lines.append(current_field.strip())

        # 解析每个字段
        formatted_fields = []
        primary_keys = []

        for field_line in field_lines:
            field_line = field_line.strip()

            # 跳过约束定义
            if (field_line.upper().startswith('CONSTRAINT') or
                field_line.upper().startswith('PRIMARY KEY') or
                field_line.upper().startswith('FOREIGN KEY') or
                field_line.upper().startswith('INDEX') or
                field_line.upper().startswith('KEY')):

                # 提取主键信息
                if 'PRIMARY KEY' in field_line.upper():
                    pk_match = re.search(r'PRIMARY KEY\s*\(\s*\[([^\]]+)\]', field_line, re.IGNORECASE)
                    if pk_match:
                        primary_keys.append(pk_match.group(1))
                continue

            # 解析字段定义
            field_info = parse_field_definition(field_line)
            if field_info:
                formatted_fields.append(field_info)

        # 标记主键字段
        for field in formatted_fields:
            if field['name'] in primary_keys:
                field['is_primary'] = True

        # 生成格式化输出
        result = f"表名：[{table_name}]  {table_desc}\n"

        if formatted_fields:
            result += "      字段："
            for i, field in enumerate(formatted_fields):
                if i > 0:
                    result += "                 "

                field_desc = field['name']
                field_desc += f"  {field['type']}"

                if field.get('is_primary'):
                    field_desc += "  主键"
                elif field.get('not_null'):
                    field_desc += "  非空"

                if field.get('comment'):
                    field_desc += f"  {field['comment']}"
                else:
                    # 根据字段名推测中文描述
                    field_desc += f"  {get_field_description(field['name'])}"

                result += field_desc + "\n"
        else:
            result += "      字段：无法解析字段信息\n"

        return result.rstrip()

    except Exception as e:
        return f"解析错误: {str(e)}"

def parse_field_definition(field_line):
    """
    解析单个字段定义

    Args:
        field_line: 字段定义行

    Returns:
        字段信息字典
    """
    try:
        # 提取字段名（在方括号中）
        name_match = re.search(r'\[([^\]]+)\]', field_line)
        if not name_match:
            return None

        field_name = name_match.group(1)

        # 移除字段名部分，获取剩余定义
        remaining = field_line[name_match.end():].strip()

        # 提取数据类型
        type_match = re.match(r'([A-Za-z]+(?:\([^)]+\))?)', remaining)
        if not type_match:
            return None

        field_type = type_match.group(1)

        # 检查约束
        is_primary = 'PRIMARY KEY' in field_line.upper()
        not_null = 'NOT NULL' in field_line.upper()
        auto_increment = 'AUTO_INCREMENT' in field_line.upper()

        # 提取注释（如果有）
        comment_match = re.search(r'COMMENT\s+[\'"]([^\'"]*)[\'"']', field_line, re.IGNORECASE)
        comment = comment_match.group(1) if comment_match else None

        return {
            'name': field_name,
            'type': field_type.lower(),
            'is_primary': is_primary,
            'not_null': not_null,
            'auto_increment': auto_increment,
            'comment': comment
        }

    except Exception as e:
        return None

def get_field_description(field_name):
    """
    根据字段名推测中文描述

    Args:
        field_name: 字段名

    Returns:
        中文描述
    """
    field_descriptions = {
        'id': 'ID',
        'user_id': '用户ID',
        'username': '用户名',
        'password': '密码',
        'email': '邮箱',
        'phone': '电话',
        'mobile': '手机号',
        'name': '名称',
        'title': '标题',
        'content': '内容',
        'description': '描述',
        'status': '状态',
        'type': '类型',
        'category': '分类',
        'price': '价格',
        'amount': '金额',
        'quantity': '数量',
        'date': '日期',
        'time': '时间',
        'created_at': '创建时间',
        'updated_at': '更新时间',
        'create_time': '创建时间',
        'update_time': '更新时间',
        'deleted_at': '删除时间',
        'is_deleted': '是否删除',
        'sort': '排序',
        'order': '排序',
        'level': '级别',
        'grade': '等级',
        'score': '分数',
        'remark': '备注',
        'note': '备注',
        'address': '地址',
        'company': '公司',
        'department': '部门',
        'position': '职位',
        'salary': '薪资',
        'age': '年龄',
        'gender': '性别',
        'birthday': '生日',
        'avatar': '头像',
        'image': '图片',
        'url': '链接',
        'code': '编码',
        'number': '编号'
    }

    field_name_lower = field_name.lower()

    # 精确匹配
    if field_name_lower in field_descriptions:
        return field_descriptions[field_name_lower]

    # 模糊匹配
    for key, desc in field_descriptions.items():
        if key in field_name_lower or field_name_lower in key:
            return desc

    # 默认描述
    return field_name

# 格式化多个建表语句
def format_create_statements(schema_statements):
    """
    格式化多个建表语句

    Args:
        schema_statements: 建表语句字符串（多个语句用\n\n分隔）

    Returns:
        格式化后的表结构描述
    """
    if not schema_statements or not schema_statements.strip():
        return "没有找到建表语句"

    # 分割多个建表语句
    statements = schema_statements.split('\n\n')
    formatted_results = []

    for statement in statements:
        statement = statement.strip()
        if statement and 'CREATE TABLE' in statement.upper():
            formatted = parse_create_table_statement(statement)
            formatted_results.append(formatted)

    if not formatted_results:
        return "没有找到有效的建表语句"

    return '\n\n'.join(formatted_results)

# 定义DatabaseSchemaDefinition类
class DatabaseSchemaDefinition:
    def __init__(self):
        # db_schema_definition = self.getCreateSql()
        # print(f"db_schema_definition{db_schema_definition}")
       print("-------")


    def get_create_sql(self, db_name):
        """
        获取dyl_srm数据库中所有的建表语句，并将表名和字段名用[]括起来，
        所有建表语句结束用;分开，每个建表语句间空一行，最终生成字符串

        """
        try:
            # 检查数据库连接是否可用
            if dbAccess is None:
                print("Database connection is not available")
                return self._get_default_schema()

            # 使用dbAccess对象获取所有表名
            tables_df = dbAccess.run_sql("SHOW TABLES")

            # 存储所有建表语句的列表
            create_statements = []

            # 遍历限定数量的表，获取建表语句
            for i, (_, row) in enumerate(tables_df.iterrows()):
                table_name = row[0]  # 第一列是表名
                print(f"处理表: {table_name}")

                try:
                    create_table_df = dbAccess.run_sql(f"SHOW CREATE TABLE `{table_name}`")
                    create_table = create_table_df.iloc[0, 1]  # 第二列是建表语句

                    # 将原始的建表语句转换为需要的格式
                    # 使用一个正则表达式替换所有的反引号为方括号
                    create_table = sub(r'`([^`]*)`', r'[\1]', create_table)

                    # 确保每个语句以分号结尾
                    if not create_table.strip().endswith(';'):
                        create_table += ';'

                    # 添加到列表
                    create_statements.append(create_table)
                except Exception as table_error:
                    print(f"处理表 {table_name} 时出错: {str(table_error)}，将跳过此表")
                    continue

            # 将所有建表语句合并为一个字符串，每个语句之间空一行
            return "\n\n".join(create_statements)

        except Exception as e:
            print(f"获取建表语句时出错: {str(e)}")
            # 如果出错，返回一个默认的建表语句
            return self._get_default_schema()

    def _get_default_schema(self):
        """返回默认的建表语句"""
        return """
        CREATE TABLE [default_table]
        (
            [id] INT NOT NULL AUTO_INCREMENT,
            [name] VARCHAR(255),
            PRIMARY KEY ([id])
        );
        """

class FilterDbSchemaAgent:
    def __init__(self):
        # super().__init__("filter_db_schema_agent")
        self.model_client = model_client
        self._prompt = f"""
        你是一名专业的数据库建表语句过滤专家。你的任务是根据用户查询从数据库建表语句中过滤出需要的建表语句。
        """

    def filter_db_schema(self, user_query=None, save_to_vector_db=True):
        """
        根据用户查询从数据库中过滤出需要的建表语句，并可选择保存到Milvus向量数据库

        Args:
            user_query: 用户查询
            save_to_vector_db: 是否将建表语句保存到Milvus向量数据库

        Returns:
            过滤后的建表语句
        """
        # 获取所有建表语句
        _scf = DatabaseSchemaDefinition()
        db_schema = _scf.get_create_sql("sss")
        print(f"db_schema: {db_schema}")

        # 如果需要保存到Milvus，先将所有建表语句分割并保存
        if save_to_vector_db and MILVUS_AVAILABLE:
            try:
                # 将字符串按空行分割为单独的建表语句
                schema_statements = db_schema.split('\n\n')
                saved_count = 0

                for statement in schema_statements:
                    if statement.strip():
                        # 提取表名
                        match = re.search(r'CREATE TABLE \[(\w+)\]', statement)
                        if match:
                            table_name = match.group(1)
                            # 保存到Milvus
                            if save_to_milvus(table_name, statement):
                                saved_count += 1

                print(f"Successfully saved {saved_count} table statements to Milvus")
            except Exception as e:
                print(f"Error saving to Milvus: {str(e)}")

        # 使用大模型过滤建表语句
        _prompt = f"""
        根据```{user_query}```的提问从{db_schema}中过滤出需要的建表语句
           ```
        """

        # 如果启用了Milvus，先尝试从 Milvus 中搜索相关的建表语句
        if MILVUS_AVAILABLE and user_query:
            try:
                milvus_results = search_milvus(user_query, top_k=5)
                if milvus_results:
                    print(f"Found {len(milvus_results)} relevant table statements from Milvus")
                    # 将搜索结果组合成字符串
                    filtered_schema = "\n\n".join([result['statement'] for result in milvus_results])
                    # 格式化建表语句为易读格式
                    formatted_schema = format_create_statements(filtered_schema)
                    return formatted_schema
            except Exception as e:
                print(f"Error searching Milvus: {str(e)}")

        # 如果没有启用Milvus或者搜索失败，使用大模型过滤
        # 使用非流式方式获取结果，避免异步生成器问题
        try:
            # 使用非流式模式
            agent_non_stream = AssistantAgent(
                name="filter_db_schema",
                model_client=self.model_client,
                system_message=_prompt,
                model_client_stream=False,  # 关闭流式模式
            )

            result = agent_non_stream.run(task=_prompt)

            # 处理结果
            raw_result = ""
            if hasattr(result, 'messages') and result.messages:
                # 获取最后一条消息的内容
                last_message = result.messages[-1]
                if hasattr(last_message, 'content'):
                    raw_result = last_message.content
                else:
                    raw_result = str(last_message)
            else:
                raw_result = str(result)

            # 尝试格式化结果
            try:
                formatted_result = format_create_statements(raw_result)
                return formatted_result
            except Exception as format_error:
                print(f"Error formatting result: {str(format_error)}")
                return raw_result

        except Exception as e:
            print(f"Error processing query: {str(e)}")
            # 如果大模型处理失败，返回原始的 db_schema
            return db_schema


class StreamResponseCollector:
    """流式响应收集器，用于收集智能体产生的流式输出"""

    def __init__(self):
        """初始化流式响应收集器"""
        self.callback: Optional[Callable[[ClosureContext, ResponseMessage, MessageContext], Awaitable[None]]] = None
        self.user_input: Optional[Callable[[str, CancellationToken], Awaitable[str]]] = None

    def set_callback(self, callback: Callable[[ClosureContext, ResponseMessage, MessageContext], Awaitable[None]]) -> None:
        """设置回调函数

        Args:
            callback: 用于处理响应消息的异步回调函数
        """
        self.callback = callback

    def set_user_input(self, user_input: Callable[[str, CancellationToken], Awaitable[str]]) -> None:
        """设置用户输入函数"""
        self.user_input = user_input

# 查询分析智能体，负责分析用户查询和表结构的关系
@type_subscription(topic_type=query_analyzer_topic_type)
class QueryAnalyzerAgent(RoutedAgent):
    def __init__(self, db_schema=None, db_type=DB_TYPE, input_func=None):
        super().__init__("query_analyzer_agent")
        self.model_client = model_client
        # 数据训练进向量数据库
        self.db_schema = db_schema or db_schema_definition

        self.db_type = db_type
        self.input_func = input_func
        self._prompt = f"""
            你是一名专业的数据库分析与生成SQL命令的分析专家。你的任务是深入分析用户的自然语言查询，并结合给定的数据库表结构信息，生成一份完整详细的关于生成SQL命令的分析报告。这份报告将作为后续指导另一个大模型生成精确SQL命令的关键依据。
            **核心目标：**

            基于用户查询和数据库结构，输出一份结构化的报告，详细描述如何将用户意图转化为SQL查询的关键步骤和考虑因素。

            **你需要处理以下信息：**

            * **数据库类型：** {self.db_type}
            * **数据库结构：**
                ```sql
                {self.db_schema}
                ```
            * **用户原始问题：** [query]

            **请按照以下步骤进行分析并生成报告：**

            1.  **查询意图深度分析：**
                * 详细描述用户提出的自然语言查询的核心意图。用户希望从数据库中检索或操作哪些数据？他们的最终目标是什么？
                * 识别查询中涉及的关键概念和实体。

            2.  **主要实体与关系识别：**
                * 根据用户查询和数据库结构，识别出查询所涉及的主要实体（对应数据库中的表）。
                * 分析这些实体之间的关系（例如，一对一、一对多、多对多），并确定可能需要进行的表连接。

            3.  **所需表与字段精确确定：**
                * 列出用户查询明确或暗示需要使用的所有表名。
                * 列出需要从这些表中检索的所有字段名。如果需要进行计算或聚合操作，也请在此处说明。

            4.  **潜在歧义与缺失信息识别：**
                * 分析用户查询中可能存在的歧义或不明确之处。例如，用户是否使用了模糊的术语、未指定具体的条件、或者查询范围不清晰？
                * 指出生成完整且准确SQL语句所需的任何缺失信息。

            5.  **SQL操作类型与结构初步构思：**
                * 基于对用户意图的理解，确定需要执行的SQL操作类型（例如：SELECT, INSERT, UPDATE, DELETE）。对于查询操作，需要进一步考虑是否需要聚合函数（SUM, AVG, COUNT, MAX, MIN）、分组（GROUP BY）、排序（ORDER BY）、限制结果数量（LIMIT）等。
                * 初步构思SQL查询语句的基本结构框架，包括涉及的表、大致的连接方式和主要的条件逻辑。

            6.  **报告输出 - 请严格按照以下格式：**

            ### SQL 命令生成报告

            #### 1. 用户原始问题：
            [在此处填写用户的自然语言查询]

            #### 2. 数据库类型：
            {self.db_type}

            #### 3. 数据库结构：
            ```sql
            {self.db_schema}
            ```

            #### 4. 查询意图描述：
            [对用户查询意图进行详细描述]

            #### 5. 需要使用的表名列表：
            - [表名1]
            - [表名2]
            - ...

            #### 6. 需要使用的字段列表：
            - 表名1: [字段1], [字段2], ...
            - 表名2: [字段1], [字段2], ...
            - ...
            （请注明是否需要使用聚合函数，例如：`SUM(sales_amount)`）

            #### 7. 需要的表连接描述：
            - [如果需要连接，描述连接的表以及连接条件（例如：`orders` 表通过 `user_id` 连接到 `users` 表）]
            - [如果不需要连接，说明原因]

            #### 8. 筛选条件描述：
            - [描述用户查询中隐含或明确要求的筛选条件 (例如：`WHERE status = '已发货'`)]
            - [如果存在多个筛选条件，请说明它们之间的逻辑关系 (例如：AND, OR)]

            #### 9. 分组描述：
            - [描述是否需要对结果进行分组 (GROUP BY)，以及分组的字段是什么]
            - [说明分组后是否需要进行聚合操作]

            #### 10. 排序描述：
            - [描述是否需要对结果进行排序 (ORDER BY)，以及排序的字段和排序方式 (ASC/DESC)]

            #### 11. 潜在歧义与缺失信息：
            - [列出用户查询中存在的任何潜在歧义，并说明可能导致不同SQL解释的情况]
            - [指出生成完整SQL语句所需的缺失信息，并说明需要用户提供哪些额外细节]

            #### 12. 初步的SQL查询结构草案：
            ```sql
            -- 基于以上分析的初步 SQL 查询结构
            SELECT [在此处填写需要选择的字段]
            FROM [在此处填写需要使用的表名]
            [在此处填写需要的连接 (例如：INNER JOIN table2 ON ...)]
            WHERE [在此处填写筛选条件]
            [在此处填写分组 (例如：GROUP BY ...)]
            [在此处填写排序 (例如：ORDER BY ...)]
            [在此处填写限制结果数量 (例如：LIMIT ...)]
            ;
            ```
        请确保你的报告内容详尽、准确，能够清晰地反映用户查询的意图以及如何将其转化为可执行的SQL命令。这份报告的质量将直接影响后续SQL语句生成的准确性。
        注意：如果用户在第2次、第3次等提出了修改建议，每次不需要将整篇报告输出。
        """

    @message_handler
    async def handle_message(self, message: QueryMessage, ctx: MessageContext) -> None:
        """处理接收到的消息，分析查询意图和所需表结构"""
        # 创建agent并执行任务
        self._prompt = self._prompt.replace("[query]", message.query)
        agent = AssistantAgent(
            name="query_analyzer",
            model_client=self.model_client,
            system_message=self._prompt,
            model_client_stream=True,
        )
        memory = ListMemory()
        analysis_content = ""
        # 如果需要用户对分析报告进行反馈
        if self.input_func:
            user_proxy = UserProxyAgent(
                name="user_proxy",
                input_func=self.input_func
            )
            termination_en = TextMentionTermination("APPROVE")
            termination_zh = TextMentionTermination("同意")
            # 支持用户对分析报告进行多次修改
            team = RoundRobinGroupChat([agent, user_proxy], termination_condition=termination_en | termination_zh, )
            stream = team.run_stream(task=message.query)
            async for msg in stream:
                # 模拟流式输出
                if isinstance(msg, ModelClientStreamingChunkEvent):
                    await self.publish_message(ResponseMessage(source="查询分析智能体", content=msg.content),
                                               topic_id=TopicId(type=stream_output_topic_type, source=self.id.key))
                    continue
                # 记录每次对话历史记录
                if isinstance(msg, TextMessage):
                    # 保存历史记忆
                    await memory.add(MemoryContent(content=msg.model_dump_json(), mime_type=MemoryMimeType.JSON.value))
                    continue
                # 等待用户输入对分析报告的修改建议
                if isinstance(msg, UserInputRequestedEvent) and msg.source == "user_proxy":
                    await self.publish_message(ResponseMessage(source=msg.source, content="请输入修改建议或者直接点击同意"),
                                               topic_id=TopicId(type=stream_output_topic_type, source=self.id.key))
                    continue
        else:
            # 如果用户没有参与修改，则直接生成分析报告
            stream = agent.run_stream(task=message.query)
            async for event in stream:
                if isinstance(event, ModelClientStreamingChunkEvent):
                    # 确保内容以Markdown格式正确渲染
                    await self.publish_message(
                        ResponseMessage(source="查询分析智能体", content=event.content),
                        topic_id=TopicId(type=stream_output_topic_type, source=self.id.key)
                    )
                    continue
                if isinstance(event, TextMessage):
                    await memory.add(MemoryContent(content=event.model_dump_json(), mime_type=MemoryMimeType.JSON.value))

        await self.publish_message(
            ResponseMessage(
                source="查询分析智能体",
                content="\n\n分析已完成",
                is_final=True,  # 关键标记
            ),
            topic_id=TopicId(type=stream_output_topic_type, source=self.id.key)
        )

        # 将 ListMemory 转换为可序列化的格式
        memory_content = []
        for content in memory.content:  # 使用.content属性而不是.contents
            memory_content.append({
                "content": content.content,
                "mime_type": content.mime_type,  # 将MemoryMimeType转换为字符串
                "metadata": content.metadata  # 加入metadata字段以保存完整信息
            })

        analysis_message = AnalysisMessage(
            query=message.query,
            memory_content=memory_content,  # 使用转换后的内容
            analysis=analysis_content,
            role="assistant"
        )

        await self.publish_message(
            analysis_message,
            topic_id=TopicId(type=sql_generator_topic_type, source=self.id.key)
        )

# SQL生成智能体，负责将自然语言转换为SQL
@type_subscription(topic_type=sql_generator_topic_type)
class SqlGeneratorAgent(RoutedAgent):
    def __init__(self, db_schema=None, db_type=DB_TYPE):
        super().__init__("sql_generator_agent")
        self.model_client = model_client
        self.db_schema = db_schema or db_schema_definition
        self.db_type = db_type
        self._prompt = f"""
        你是一名专业的SQL转换专家。你的任务是基于上下文信息及SQL命令生成报告，将用户的自然语言查询转换为精确的SQL语句。

        ## 生成SQL的指导原则：

        1.  **严格遵循报告中的分析：** 仔细阅读并理解上述的SQL命令生成报告，包括查询意图、需要使用的表和字段、连接方式、筛选条件、分组和排序要求。
        2.  **生成有效的SQL语句：** 仅输出符合 {self.db_type} 数据库语法的有效SQL语句，不要添加任何额外的解释或说明。
        3.  **准确表达筛选条件：** 报告中如有筛选条件描述，务必在生成的SQL语句中准确实现。
        4.  **正确使用表连接：** 按照报告中"需要的表连接描述"进行表连接，并确保连接条件正确。
        5.  **实现分组和聚合：** 如果报告中指示需要进行分组（GROUP BY）或聚合操作（例如 SUM, COUNT, AVG），请在SQL语句中正确实现。
        6.  **实现排序：** 按照报告中"排序描述"的要求，使用 ORDER BY 子句对结果进行排序。
        7.  **考虑数据库特性：** 生成的SQL语句应符合 {self.db_type} 数据库的特定语法和函数。
        8.  **SQL格式规范：** 使用清晰可读的SQL格式，适当添加换行和缩进，以提高可读性。
        9.  **避免使用不支持的语法：** 不要使用 {self.db_type} 数据库不支持的特殊语法或函数。
        10. **仅生成SQL：** 最终输出结果必须是纯粹的SQL查询语句，没有任何额外的文本。

        特别注意：最终只生成一条您认为最符合用户查询需求的SQL语句。
        """

    @message_handler
    async def handle_analysis_message(self, message: AnalysisMessage, ctx: MessageContext) -> None:
        """处理AnalysisMessage类型的消息"""
        # 从memory_content重建ListMemory对象
        memory = ListMemory()
        # 直接设置内容属性，效率更高
        memory_contents = []
        for item in message.memory_content:
            memory_contents.append(MemoryContent(
                content=item["content"],
                mime_type=item["mime_type"],
                metadata=item.get("metadata", None)  # 读取可能存在的metadata
            ))
        memory.content = memory_contents  # 利用ListMemory的content属性直接设置内容

        agent = AssistantAgent(
            name="sql_generator",
            model_client=self.model_client,
            system_message=self._prompt,
            memory=[memory],  # 使用重建的memory
            model_client_stream=True,
        )

        result = await agent.run(task=message.query)
        sql_content = result.messages[-1].content

        # 首先将SQL内容发送为流式消息，供前端实时更新
        await self.publish_message(
            ResponseMessage(source="SQL生成智能体", content=sql_content),
            topic_id=TopicId(type=stream_output_topic_type, source=self.id.key)
        )

        # 发送SQL内容为final_sql类型，触发前端SQL语句区域显示
        await self.publish_message(
            ResponseMessage(
                source="SQL生成智能体",
                content="SQL语句已生成",
                is_final=True,
                result={"sql": sql_content}  # 使用正确的格式包含SQL语句
            ),
            topic_id=TopicId(type=stream_output_topic_type, source=self.id.key)
        )

        await self.publish_message(
            SqlMessage(query=message.query, sql=sql_content),
            topic_id=TopicId(type=sql_explainer_topic_type, source=self.id.key)
        )


# SQL解释智能体，负责解释SQL语句的含义
@type_subscription(topic_type=sql_explainer_topic_type)
class SqlExplainerAgent(RoutedAgent):
    def __init__(self, db_schema=None, db_type=DB_TYPE):
        super().__init__("sql_explainer_agent")
        self.model_client = model_client
        self.db_schema = db_schema or db_schema_definition
        self.db_type = db_type
        self._prompt = f"""
        你是一名专业的SQL解释专家，你的任务是以准确、易懂的方式向非技术人员解释给定的SQL语句的含义和作用。

        ## 数据库类型
        {self.db_type}

        ## 数据库结构
        ```sql
        {self.db_schema}
        ```

        ## 用户问题
        [query]

        ## 需要解释的SQL语句
        [sql]

        ## 规则

        1.  **使用通俗易懂的语言：** 解释应该避免使用过于专业或技术性的术语。目标是让没有任何编程或数据库知识的人也能理解。
        2.  **准确且全面地解释：** 确保解释的准确性，并覆盖SQL语句的主要功能和逻辑。
        3.  **解释关键子句：** 针对SQL语句中的每个主要子句（例如 `SELECT`, `FROM`, `WHERE`, `GROUP BY`, `ORDER BY`, `JOIN` 等）解释其作用和目的。
        4.  **说明查询结果：** 清晰地描述执行这条SQL语句后，预计会从数据库中返回什么类型的数据和结果。
        5.  **解释复杂特性：**
            * **聚合函数：** 如果SQL语句中使用了聚合函数（如 `SUM`, `AVG`, `COUNT`, `MAX`, `MIN`），解释这些函数的作用以及它们是如何计算结果的。
            * **表连接：** 如果使用了表连接（如 `JOIN`），解释为什么要进行连接，以及连接是如何根据相关字段将不同表中的数据关联起来的。可以结合数据库结构进行解释。
            * **子查询：** 如果使用了子查询（嵌套查询），解释子查询的目的以及它是如何帮助主查询获取所需数据的。
        6.  **结合数据库结构：** 在解释过程中，可以适当引用提供的数据库表结构，帮助理解表名、字段名的含义以及表之间的关系。例如，解释 `users.name` 时，可以说明 `name` 是 `users` 表中的一个字段，用于存储用户的姓名。
        7.  **保持简洁明了：** 尽量用简短的句子表达清楚意思，避免冗长的描述。解释的长度一般不超过200字。
        8.  **直接解释提供的SQL：** 你的解释应该直接针对用户问题 `[query]` 和 `[sql]` 部分提供的具体SQL代码。

        **示例解释框架：**

        "这条SQL语句的作用是[整体功能描述]。它首先从 `[表名]` 表中[FROM子句的解释]。然后，它会筛选出满足[WHERE子句的解释]的记录。如果使用了 `GROUP BY`，则会按照[GROUP BY子句的解释]进行分组，并且可能会使用[聚合函数]计算每个组的结果。最后，结果可能会按照[ORDER BY子句的解释]进行排序。总的来说，这条语句会返回[对查询结果的总结性描述]。"
        """

    @message_handler
    async def handle_message(self, message: SqlMessage, ctx: MessageContext) -> None:
        """处理接收到的消息，解释SQL语句"""
        self._prompt = self._prompt.replace("[sql]", message.sql).replace("[query]", message.query)
        agent = AssistantAgent(
            name="sql_explainer",
            model_client=self.model_client,
            system_message=self._prompt,
            model_client_stream=True,
        )

        stream = agent.run_stream(task=f"解释以下SQL语句：\n{message.sql}")
        explanation_content = ""

        async for event in stream:
            if isinstance(event, ModelClientStreamingChunkEvent):
                await self.publish_message(
                    ResponseMessage(source="SQL解释智能体", content=event.content),
                    topic_id=TopicId(type=stream_output_topic_type, source=self.id.key)
                )
            elif isinstance(event, TaskResult):
                explanation_content = event.messages[-1].content.strip()

        await self.publish_message(
            ResponseMessage(source="SQL解释智能体", content="\n\n解释完成", is_final=True,),
            topic_id=TopicId(type=stream_output_topic_type, source=self.id.key)
        )

        await self.publish_message(
            SqlExplanationMessage(
                query=message.query,
                sql=message.sql,
                explanation=explanation_content
            ),
            topic_id=TopicId(type=sql_executor_topic_type, source=self.id.key)
        )


# SQL执行智能体，负责模拟执行SQL并返回结果
@type_subscription(topic_type=sql_executor_topic_type)
class SqlExecutorAgent(RoutedAgent):
    def __init__(self):
        super().__init__("sql_executor_agent")
    @message_handler
    async def handle_message(self, message: SqlExplanationMessage, ctx: MessageContext) -> None:
        """处理接收到的消息，根据SQL生成模拟数据"""

        sql = message.sql.replace("```sql", "").replace("```", "")
        results = dbAccess.run_sql(sql)
        # 发送执行完成的消息
        await self.publish_message(
            ResponseMessage(source="SQL执行智能体", content=f"SQL执行完成，获取到{len(results)}条结果"),
            topic_id=TopicId(type=stream_output_topic_type, source=self.id.key)
        )

        results = results.to_dict("records")
        # 然后立即发送数据结果，这样前端会显示数据表格
        await self.publish_message(
            ResponseMessage(
                source="SQL执行智能体",
                content="查询结果数据",
                is_final=True,
                result={
                    "results": results,  # 只包含结果数据
                }
            ),
            topic_id=TopicId(type=stream_output_topic_type, source=self.id.key)
        )

        # 继续正常的智能体流程，将结果传递给可视化推荐智能体
        await self.publish_message(
            SqlResultMessage(
                query=message.query,
                sql=message.sql,
                explanation=message.explanation,
                results=results
            ),
            topic_id=TopicId(type=visualization_recommender_topic_type, source=self.id.key)
        )


# 数据可视化推荐智能体，负责建议合适的可视化方式
@type_subscription(topic_type=visualization_recommender_topic_type)
class VisualizationRecommenderAgent(RoutedAgent):
    def __init__(self):
        super().__init__("visualization_recommender_agent")
        self.model_client = model_client
        self._prompt = """```
        你是一名专业的数据可视化专家，负责根据提供的用户指令、SQL查询及其结果数据，推荐最合适的数据可视化方式，并给出详细的配置建议。

        ## 规则

        1.  **分析SQL查询：** 理解SQL查询的目标，例如是进行趋势分析、比较不同类别的数据、展示数据分布还是显示详细数据。
        2.  **分析查询结果数据结构：** 检查返回的数据包含哪些字段，它们的数据类型（数值型、分类型等），以及数据的组织方式（例如，是否包含时间序列、类别标签、数值指标等）。
        3.  **基于数据结构和查询目标推荐可视化类型：**
            * 如果数据涉及**时间序列**且需要展示**趋势**，推荐 `"line"` (折线图)。
            * 如果需要**比较不同类别**的**数值大小**，推荐 `"bar"` (柱状图)。
            * 如果需要展示**各部分占总体的比例**，且类别数量不多，推荐 `"pie"` (饼图)。需要确保数值型字段是总量的一部分。
            * 如果需要展示**两个数值变量之间的关系**或**数据点的分布**，推荐 `"scatter"` (散点图)。
            * 如果数据结构复杂、细节重要，或者无法找到合适的图表类型清晰表达，推荐 `"table"` (表格)。
        4.  **提供详细的可视化配置建议：** 根据选择的可视化类型，提供具体的配置参数。
            * **通用配置：** `"title"` (图表标题，应简洁明了地概括图表内容)。
            * **柱状图 (`"bar"`):**
                * `"xAxis"` (X轴字段名，通常是分类型字段)。
                * `"yAxis"` (Y轴字段名，通常是数值型字段)。
                * `"seriesName"` (系列名称，如果只有一个系列可以省略)。
            * **折线图 (`"line"`):**
                * `"xAxis"` (X轴字段名，通常是时间或有序的分类型字段)。
                * `"yAxis"` (Y轴字段名，通常是数值型字段)。
                * `"seriesName"` (系列名称，如果只有一个系列可以省略)。
            * **饼图 (`"pie"`):**
                * `"nameField"` (名称字段名，通常是分类型字段，用于显示饼图的标签)。
                * `"valueField"` (数值字段名，用于计算每个扇区的大小)。
                * `"seriesName"` (系列名称，如果只有一个系列可以省略)。
            * **散点图 (`"scatter"`):**
                * `"xAxis"` (X轴字段名，通常是数值型字段)。
                * `"yAxis"` (Y轴字段名，通常是数值型字段)。
                * `"seriesName"` (系列名称，如果只有一个系列可以省略)。
            * **表格 (`"table"`):** 不需要特定的坐标轴或系列配置，可以考虑添加 `"columns"` 字段，列出需要在表格中显示的字段名。
        5.  **输出格式必须符合如下JSON格式:**

            ```json
            {
                "type": "可视化类型",
                "config": {
                    "title": "图表标题",
                    "xAxis": "X轴字段名",
                    "yAxis": "Y轴字段名",
                    "seriesName": "系列名称"
                    // 其他配置参数根据可视化类型添加
                }
            }
            ```

            对于饼图：

            ```json
            {
                "type": "pie",
                "config": {
                    "title": "图表标题",
                    "nameField": "名称字段名",
                    "valueField": "数值字段名",
                    "seriesName": "系列名称"
                }
            }
            ```

            对于表格：

            ```json
            {
                "type": "table",
                "config": {
                    "title": "数据表格",
                    "columns": ["字段名1", "字段名2", ...]
                }
            }
            ```

        ## 支持的可视化类型

        - `"bar"`: 柱状图
        - `"line"`: 折线图
        - `"pie"`: 饼图
        - `"scatter"`: 散点图
        - `"table"`: 表格(对于不适合图表的数据)
        特别注意：如果用户有对生成的图表有明确的特定要求，一定要严格遵守用户的指令。例如用户明确要求生成饼状图，就不能生成柱状图。
        """

    @message_handler
    async def handle_message(self, message: SqlResultMessage, ctx: MessageContext) -> None:
        """处理接收到的消息，推荐可视化方式"""
        # 发送处理中消息
        await self.publish_message(
            ResponseMessage(source="可视化推荐智能体", content="正在分析数据，生成可视化建议..."),
            topic_id=TopicId(type=stream_output_topic_type, source=self.id.key)
        )
        results_json = json.dumps(message.results, ensure_ascii=False)
        self._prompt = self._prompt.replace("[sql]", message.sql).replace("[results]", results_json)
        agent = AssistantAgent(
            name="visualization_recommender",
            model_client=self.model_client,
            system_message=self._prompt,
            model_client_stream=True,
        )
        task = f"""
        ## 用户指令
         {message.query}

        ## 待分析的SQL查询
        {message.sql}

        ## SQL查询结果数据
        ```json
        {results_json}
        ```

        请根据提供的上述信息，分析并输出最合适的可视化类型和配置，输出必须是有效的JSON
        """

        result = await agent.run(task=task)
        visualization_json = result.messages[-1].content

        try:
            visualization = json.loads(visualization_json.replace("```json", "").replace("```", ""))
        except json.JSONDecodeError:
            visualization = {
                "type": "bar",
                "config": {
                    "title": "数据可视化",
                    "xAxis": list(message.results[0].keys())[0] if message.results else "x",
                    "yAxis": list(message.results[0].keys())[1] if message.results and len(message.results[0].keys()) > 1 else "y"
                }
            }

        # 如果是表格，则直接返回，因为表格已经在上个智能体中已经呈现
        if visualization.get("type") == "table":
            await self.publish_message(
                ResponseMessage(source="可视化推荐智能体", content="可视化分析已完成", is_final=True,),
                topic_id=TopicId(type=stream_output_topic_type, source=self.id.key)
            )
            return
        # 构建最终结果
        final_result = Text2SQLResponse(
            sql=message.sql,
            explanation=message.explanation,
            results=message.results,
            visualization_type=visualization.get("type", "bar"),
            visualization_config=visualization.get("config", {})
        )

        await self.publish_message(
            ResponseMessage(
                source="可视化推荐智能体",
                content="处理完成，返回最终结果",
                is_final=True,
                result=final_result.model_dump()
            ),
            topic_id=TopicId(type=stream_output_topic_type, source=self.id.key)
        )


class Text2SQLService:
    """Text2SQL服务类，处理自然语言到SQL转换的全流程"""

    def __init__(self, db_type: str = DB_TYPE):
        """初始化Text2SQL服务

        Args:
            db_type: 数据库类型，默认为DB_TYPE常量
        """
        self.db_type = db_type

    async def process_query(self, query: str, collector: StreamResponseCollector = None):
        """处理自然语言查询，返回SQL和结果"""
        fdsa=FilterDbSchemaAgent()
        _db_schema = fdsa.filter_db_schema(user_query=query, save_to_vector_db=False)

        # 创建运行时
        runtime = SingleThreadedAgentRuntime()

        # 使用register方法注册所有智能体

        await QueryAnalyzerAgent.register(runtime, query_analyzer_topic_type,
                                         lambda: QueryAnalyzerAgent(db_type=self.db_type, db_schema= _db_schema, input_func=collector.user_input))
        await SqlGeneratorAgent.register(runtime, sql_generator_topic_type,
                                        lambda: SqlGeneratorAgent(db_type=self.db_type))
        await SqlExplainerAgent.register(runtime, sql_explainer_topic_type,
                                        lambda: SqlExplainerAgent(db_type=self.db_type))
        await SqlExecutorAgent.register(runtime, sql_executor_topic_type,
                                       lambda: SqlExecutorAgent())
        await VisualizationRecommenderAgent.register(runtime, visualization_recommender_topic_type,
                                                   lambda: VisualizationRecommenderAgent())

        # 注册收集器
        await ClosureAgent.register_closure(
            runtime,
            "stream_collector_agent",
            collector.callback,
            subscriptions=lambda: [
                TypeSubscription(
                    topic_type=stream_output_topic_type,
                    agent_type="stream_collector_agent"
                )
            ],
        )

        # 启动运行时
        runtime.start()

        # 发送初始消息
        await runtime.publish_message(
            QueryMessage(query=query),
            topic_id=DefaultTopicId(type=query_analyzer_topic_type)
        )
        # 等待处理完成
        await runtime.stop_when_idle()

        # 关闭运行时
        await runtime.close()
